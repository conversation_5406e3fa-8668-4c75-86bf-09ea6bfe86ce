/*
 * @Date: 2024-11-20 10:41:09
 * @Description: 首页
 */

import {
  useReachBottom,
  usePullDownRefresh,
  useDidShow,
  useDidHide,
} from '@tarojs/taro'
import { useEffect, useRef, useState } from 'react'
import { View as V } from '@tarojs/components'
import s from './index.module.scss'
import { actions, dispatch, getState, useSelector } from '@/core/store'
import Page from '@/components/Page'
import Header from './components/Header'
import Pos from './components/Pos'
import Filter from './components/Filter'
import List from './components/List'
import useHomeLocation from '@/hooks/useHomeLocation'
import useStates from '@/hooks/useStates'
import Login from './components/Login'
import useRefValue from '@/hooks/useRefValue'
import { queryJobDetail } from '@/utils/helper/recruit'
import { getAreaById } from '@/utils/location'
import ResumePublishPop from '@/components/ResumePublishPop'

const defaultClassify = {
  id: -1,
  name: '推荐',
  recommended: 1,
}

const p: Record<string, any> = {}

export default () => {
  /** 列表数据 */
  const [list, setList] = useState([])
  const token = useSelector((state) => state.storage.token)
  const homeClassify: any = useSelector((state) => state.storage.homeClassify)
  const homeNearbyLocation = useSelector(
    (state) => state.storage.homeNearbyLocation,
  )
  const [activeClassify, setActiveClassify] = useState(defaultClassify.id)
  // 弹出 发布找活弹窗
  const [publishResumePop, setPublishResumePop] = useState(false)
  const [listType, setListType] = useState(
    Number($.router.query.listType) || 2,
  ) // 列表类型
  const allClassify = [defaultClassify, ...homeClassify]

  const [result, setResult] = useStates({
    loadingStatus: undefined as any, // 'more' | 'done'
    skeleton: true, // 骨架屏显示
    lastTime: '',
    currentPage: 1,
  })

  const { lastTime, currentPage, loadingStatus, skeleton } = result

  /** 获取定位 */
  const homeLocation = useHomeLocation((nextHomeLocation) => {
    onRefResh({ page: 1 }, { nextHomeLocation })
  })

  const oldHomeLocation = useRef(homeLocation.data.areaId)

  useDidShow(() => {
    if (homeLocation.data.areaId !== oldHomeLocation.current) {
      onRefResh({ page: 1 })
    }
  })

  useDidHide(() => {
    oldHomeLocation.current = homeLocation.data.areaId
  })

  const onRefResh = async (
    { page = currentPage },
    {
      nextListType = listType,
      nextActiveClassify = activeClassify,
      nextHomeLocation = homeLocation,
      nextHomeNearbyLocation = homeNearbyLocation,
      nextHomeClassify = homeClassify,
    } = {},
  ) => {
    if (p.done === false) {
      return
    }
    p.nextListType = nextListType
    p.nextActiveClassify = nextActiveClassify
    p.done = false
    const locationValues = getLocation({
      listType: nextListType,
      homeNearbyLocation: nextHomeNearbyLocation,
      homeLocation: nextHomeLocation,
    })
    // 港澳台，提示不支持
    if (nextListType !== 3 && [33, 34, 35].includes(locationValues.areaId)) {
      p.done = true
      $.msg('地区暂不支持')
      setList([])
      setResult({
        skeleton: false, // 骨架屏显示
        loadingStatus: 'done',
      })
      return
    }
    // 如果是选择的附近，并且没有获取到地址，则不请求数据
    if (
      nextListType === 3
      && nextHomeNearbyLocation.success
      && !locationValues.areaId
    ) {
      p.done = true
      $.msg('地区暂不支持')
      setList([])
      setResult({
        skeleton: false, // 骨架屏显示
        loadingStatus: 'done',
      })
      return
    }
    // 附近定位失败的情况
    if (!nextHomeNearbyLocation.success && nextListType === 3) {
      p.done = true
      setList([])
      setResult({
        skeleton: false,
        loadingStatus: 'done',
      })
      return
    }
    // 骨架屏
    if (page === 1) {
      $.taro.pageScrollTo({ scrollTop: 0, duration: 200 })
      setList([])
      setResult({ skeleton: true })
    }
    // loading状态
    if (page !== 1 && loadingStatus !== 'more') {
      setResult({ loadingStatus: 'more' })
    }
    const occ = getOcc(nextActiveClassify, nextHomeClassify)
    const [data] = await $.request['POST/job/v3/list/job/list']({
      recommended: nextActiveClassify == -1 ? 1 : 0,
      listType: nextListType,
      pageSize: 15,
      lastTime: page === 1 ? '' : lastTime,
      currentPage: page,
      ...occ,
      ...locationValues,
    })
      .catch((err) => {
        // 异常就清空
        setList([])
        setResult({
          skeleton: false, // 骨架屏显示
          lastTime: '',
          currentPage: 1,
          loadingStatus: 'done',
        })
        throw err
      })
      .finally(() => {
        setTimeout(() => {
          p.done = true
        }, 500)
      })

    let jobOptions = '推荐'
    jobOptions = nextHomeClassify.find(item => item.id == nextActiveClassify)?.name || jobOptions
    const listTypeName = nextListType == 1 ? '最新' : nextListType == 2 ? '综合' : '附近'
    const dataList = data.list.map((item, index) => {
      item = { ...item,
        pagination_location: index + 1,
        currentPage: page,
        buriedData: {
          ...item.buriedData,
          pagination: String(page),
          pagination_location: String(index + 1),
          type_options: listTypeName,
          job_options: jobOptions,
          sort_time: item.sortTime,
          topping: item.isTop ? '1' : '0',
          source_id: '1',
          location_id: page == 1 ? String(index + 1) : String(index + 1 + list.length),
          source: '招工大列表',
          occupations_type: item.occMode == 2 ? '招聘' : '订单',
        },
      }
      return item
    })
    const newList = page === 1 ? dataList : list.concat(dataList)
    setList(newList)
    if (
      p.nextListType == nextListType
      && p.nextActiveClassify == nextActiveClassify
    ) {
      setResult({
        skeleton: false, // 骨架屏显示
        lastTime: data.lastTime,
        currentPage: page,
        loadingStatus: data.list.length == 15 ? 'more' : 'done',
      })
    }
  }

  useReachBottom(() => {
    if (p.done) {
      onRefResh({ page: currentPage + 1 })
    }
  })

  usePullDownRefresh(async () => {
    await onRefResh({ page: 1 })
    $.taro.stopPullDownRefresh()
  })

  useEffect(() => {
    onRefResh({ page: 1 })
  }, [token])

  // 页面显示的时候
  useDidShow(async () => {
    const { data } = $.router
    if (data && data.origin === 'resume' && data.areaId) {
      $.router.clearData()
      const { latitude, longitude } = getState().storage.homeLocation.data
      const { current } = await getAreaById(data.areaId)
      const nextHomeLocation = {
        success: true,
        data: {
          longitude,
          latitude,
          areaId: data.areaId,
          name: current ? current.name : '',
        },
      }
      // 更新本地缓存
      dispatch(actions.storage.setItems({ homeLocation: nextHomeLocation }))
      // 页面刷新
      setListType(data.listType)
      onRefResh({ page: 1 }, { nextListType: data.listType, nextHomeLocation })
    }
  })

  // 为了防止卡片重复渲染，这里用 useRefValue
  const onClickCard = useRefValue((info) => {
    const query = {
      jobId: info.jobId,
      recommend: activeClassify == -1,
    }
    queryJobDetail(true)(query)
    $.router.push('/pages/recruit/detail', query, {
      buriedData: info.buriedData,
    })
  })

  /** 强制登录成功后--在首页--判断找活名片-弹出发布简历弹窗 (登录成功后调用--勿删） */
  const loginSuccessCallBack = async () => {
    const page = $.router.getCurrentPage()

    if (page.$taroPath.indexOf('pages/index/index') >= 0) {
      console.log('ywuifhuwhfw', page.$taroPath)
    }
    const [existResume] = await $.request['POST/resume/v3/base/exist']({}, { isNoToken: true, hideMsg: true })
    if (existResume && !existResume.exist) {
      // 调起发布找活弹窗
      setPublishResumePop(true)
    }
  }

  /** 发布简历成功后-执行事件 */
  const releaseResumeCompleted = () => {
    // 关闭弹窗
    setPublishResumePop(false)
  }

  return (
    <Page>
      <V className={s.headerTop}>
        <Header />
        <Pos
          allClassify={allClassify}
          homeClassify={homeClassify}
          activeClassify={activeClassify}
          setActiveClassify={setActiveClassify}
          onRefResh={onRefResh}
        />
        <Filter
          listType={listType}
          setListType={setListType}
          onRefResh={onRefResh}
          homeLocation={homeLocation}
          homeNearbyLocation={homeNearbyLocation}
        />
      </V>
      <V className={s.body} id="listbody">
        <List
          onClickCard={onClickCard}
          location={homeLocation}
          homeNearbyLocation={homeNearbyLocation}
          loadingStatus={loadingStatus}
          skeleton={skeleton}
          list={list}
          listType={listType}
        />
      </V>
      <Login />
      {publishResumePop
      && <ResumePublishPop
        visible={publishResumePop}
        onClose={() => setPublishResumePop(false)}
        onConfirmCbFn={releaseResumeCompleted}
      />
      }
    </Page>
  )
}

/** 获取定位信息 */
function getLocation({ listType, homeNearbyLocation, homeLocation }) {
  // 如果是点击的附近
  if (listType === 3 && homeNearbyLocation.success) {
    return {
      location: {
        latitude: homeNearbyLocation.data?.latitude,
        longitude: homeNearbyLocation.data?.longitude,
      },
      areaId: homeNearbyLocation.data?.areaId,
    }
  }
  if (listType !== 3) {
    if (!homeLocation.success) {
      return {
        areaId: homeLocation.data?.areaId,
      }
    }
    if (homeLocation.data?.latitude && homeLocation.data?.longitude) {
      return {
        location: {
          latitude: homeLocation.data?.latitude,
          longitude: homeLocation.data?.longitude,
        },
        areaId: homeLocation.data?.areaId,
      }
    }
    return {
      areaId: homeLocation.data?.areaId,
    }
  }
  return {}
}

/** 处理工种 */
function getOcc(nextActiveClassify, homeClassify) {
  let occV2: any = []
  let occType: any = []
  // 推荐列表
  if (nextActiveClassify == -1) {
    const industries = homeClassify.reduce(
      (arr, item) => [...arr, ...item.industries],
      [],
    )
    occV2 = $.uniqueArray(industries).map((item) => {
      return {
        industry: Number(item),
        occIds: homeClassify
          .filter((cl) => cl.industries.includes(item))
          .map((cl) => Number(cl.id)),
      }
    })
    occType = homeClassify.map((item) => ({ occId: Number(item.id), type: 1 }))
  } else {
    // 选的其他类别
    const item = homeClassify.find((item) => item.id == nextActiveClassify)
    occV2 = [
      { industry: Number(item.industries[0]), occIds: [Number(item.id)] },
    ]
    occType = [{ occId: Number(item.id), type: 1 }]
  }
  if (occV2.length === 0 || occType.length === 0) {
    return {}
  }
  return {
    occV2,
    occType,
  }
}
