/*
 * @Date: 2024-11-21 09:46:57
 * @Description: 强制登录弹窗
 */

import { View, Image as Img } from '@tarojs/components'
import { useEffect } from 'react'
import { getCurrentInstance } from '@tarojs/runtime'
import Body from '../Body'
import s from './index.module.scss'
import useModal from '../hooks'
import { useSelector } from '@/core/store'

export default () => {
  const res = useModal('forceLogin')
  const { options, visible, onResolve } = res
  const token = useSelector((state) => state.storage.token)
  const { success } = options

  useEffect(() => {
    if (visible) {
      const { path } = $.router
      $.report.event('mandatory_login_popup_expose', { page_path: path })
    }
  }, [visible])

  const toLogin = () => {
    $.login().then(() => {
      success && success()
      onResolve()
      const { path } = $.router
      $.report.event('mandatory_login_popup_click', {
        page_path: path,
        click_button: '点击查看',
      })
      const currentPage = getCurrentInstance()
      console.log('87902347890253780', currentPage)
      // currentPage && currentPage.page.onLoad()
      // 如果是首页，调用首页的弹出简历发布弹窗
      if (currentPage?.router?.path?.indexOf('/pages/index/index') > -1) {
        currentPage.page?.loginSuccessCallBack?.()
      }
    })
  }

  return (
    <Body visible={visible && !token} zIndex={10000} onMaskClick={() => { toLogin() }}>
      <View
        className={s.body}
        onClick={() => {
          toLogin()
        }}
      >
        <Img
          className={s.img}
          src="https://cdn.yupaowang.com/yupao_common/f06a51a9.png"
        />
      </View>
    </Body>
  )
}
